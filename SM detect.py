#!/usr/bin/env python3
"""
Android APK 国密算法检测系统
用于检测Android APK文件中的SM2、SM3、SM4国密算法实现

功能包括：
1. 建立国密算法特征数据集
2. APK分层分析（Java代码和Native代码）
3. 算法特征识别和匹配
4. 干扰项过滤
5. 算法实现验证

作者：Augment Agent
日期：2025-06-15
"""

import os
import sys
import re
import json
import zipfile
import binascii
import struct
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Set
import logging

# 第三方库导入（需要安装）
try:
    # 尝试新版本的androguard导入
    from androguard.core.apk import APK
    from androguard.core.dex import DEX
    from androguard.core.analysis.analysis import Analysis
    ANDROGUARD_AVAILABLE = True
    ANDROGUARD_VERSION = "new"
except ImportError:
    try:
        # 尝试旧版本的androguard导入
        from androguard.core.bytecodes import apk, dvm
        from androguard.core.analysis import analysis
        ANDROGUARD_AVAILABLE = True
        ANDROGUARD_VERSION = "old"
    except ImportError:
        ANDROGUARD_AVAILABLE = False
        ANDROGUARD_VERSION = None
        print("警告: androguard未安装，Java代码分析功能将不可用")

try:
    from elftools.elf.elffile import ELFFile
    from elftools.elf.sections import Section
    PYELFTOOLS_AVAILABLE = True
except ImportError:
    PYELFTOOLS_AVAILABLE = False
    print("警告: pyelftools未安装，Native代码分析功能将不可用")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SMCryptoConstants:
    """国密算法特征常量数据集"""

    def __init__(self):
        # SM2椭圆曲线参数
        self.SM2_CURVE_P = "FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF"
        self.SM2_CURVE_A = "FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC"
        self.SM2_CURVE_B = "28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93"
        self.SM2_CURVE_N = "FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123"
        self.SM2_CURVE_GX = "32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7"
        self.SM2_CURVE_GY = "BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0"

        # SM3算法常量
        self.SM3_T_ARRAY = [
            0x79CC4519, 0x7A879D8A  # T[0-15] = 0x79CC4519, T[16-63] = 0x7A879D8A
        ]
        self.SM3_IV = [
            0x7380166F, 0x4914B2B9, 0x172442D7, 0xDA8A0600,
            0xA96F30BC, 0x163138AA, 0xE38DEE4D, 0xB0FB0E4E
        ]

        # SM4算法常量
        self.SM4_SBOX = [
            0xD6, 0x90, 0xE9, 0xFE, 0xCC, 0xE1, 0x3D, 0xB7, 0x16, 0xB6, 0x14, 0xC2, 0x28, 0xFB, 0x2C, 0x05,
            0x2B, 0x67, 0x9A, 0x76, 0x2A, 0xBE, 0x04, 0xC3, 0xAA, 0x44, 0x13, 0x26, 0x49, 0x86, 0x06, 0x99,
            0x9C, 0x42, 0x50, 0xF4, 0x91, 0xEF, 0x98, 0x7A, 0x33, 0x54, 0x0B, 0x43, 0xED, 0xCF, 0xAC, 0x62,
            0xE4, 0xB3, 0x1C, 0xA9, 0xC9, 0x08, 0xE8, 0x95, 0x80, 0xDF, 0x94, 0xFA, 0x75, 0x8F, 0x3F, 0xA6,
            0x47, 0x07, 0xA7, 0xFC, 0xF3, 0x73, 0x17, 0xBA, 0x83, 0x59, 0x3C, 0x19, 0xE6, 0x85, 0x4F, 0xA8,
            0x68, 0x6B, 0x81, 0xB2, 0x71, 0x64, 0xDA, 0x8B, 0xF8, 0xEB, 0x0F, 0x4B, 0x70, 0x56, 0x9D, 0x35,
            0x1E, 0x24, 0x0E, 0x5E, 0x63, 0x58, 0xD1, 0xA2, 0x25, 0x22, 0x7C, 0x3B, 0x01, 0x21, 0x78, 0x87,
            0xD4, 0x00, 0x46, 0x57, 0x9F, 0xD3, 0x27, 0x52, 0x4C, 0x36, 0x02, 0xE7, 0xA0, 0xC4, 0xC8, 0x9E,
            0xEA, 0xBF, 0x8A, 0xD2, 0x40, 0xC7, 0x38, 0xB5, 0xA3, 0xF7, 0xF2, 0xCE, 0xF9, 0x61, 0x15, 0xA1,
            0xE0, 0xAE, 0x5D, 0xA4, 0x9B, 0x34, 0x1A, 0x55, 0xAD, 0x93, 0x32, 0x30, 0xF5, 0x8C, 0xB1, 0xE3,
            0x1D, 0xF6, 0xE2, 0x2E, 0x82, 0x66, 0xCA, 0x60, 0xC0, 0x29, 0x23, 0xAB, 0x0D, 0x53, 0x4E, 0x6F,
            0xD5, 0xDB, 0x37, 0x45, 0xDE, 0xFD, 0x8E, 0x2F, 0x03, 0xFF, 0x6A, 0x72, 0x6D, 0x6C, 0x5B, 0x51,
            0x8D, 0x1B, 0xAF, 0x92, 0xBB, 0xDD, 0xBC, 0x7F, 0x11, 0xD9, 0x5C, 0x41, 0x1F, 0x10, 0x5A, 0xD8,
            0x0A, 0xC1, 0x31, 0x88, 0xA5, 0xCD, 0x7B, 0xBD, 0x2D, 0x74, 0xD0, 0x12, 0xB8, 0xE5, 0xB4, 0xB0,
            0x89, 0x69, 0x97, 0x4A, 0x0C, 0x96, 0x77, 0x7E, 0x65, 0xB9, 0xF1, 0x09, 0xC5, 0x6E, 0xC6, 0x84,
            0x18, 0xF0, 0x7D, 0xEC, 0x3A, 0xDC, 0x4D, 0x20, 0x79, 0xEE, 0x5F, 0x3E, 0xD7, 0xCB, 0x39, 0x48
        ]

        self.SM4_CK = [
            0x00070E15, 0x1C232A31, 0x383F464D, 0x545B6269,
            0x70777E85, 0x8C939AA1, 0xA8AFB6BD, 0xC4CBD2D9,
            0xE0E7EEF5, 0xFC030A11, 0x181F262D, 0x343B4249,
            0x50575E65, 0x6C737A81, 0x888F969D, 0xA4ABB2B9,
            0xC0C7CED5, 0xDCE3EAF1, 0xF8FF060D, 0x141B2229,
            0x30373E45, 0x4C535A61, 0x686F767D, 0x848B9299,
            0xA0A7AEB5, 0xBCC3CAD1, 0xD8DFE6ED, 0xF4FB0209,
            0x10171E25, 0x2C333A41, 0x484F565D, 0x646B7279
        ]

        self.SM4_FK = [0xA3B1BAC6, 0x56AA3350, 0x677D9197, 0xB27022DC]

    def get_all_constants(self) -> Dict[str, List[str]]:
        """获取所有国密算法常量的十六进制字符串表示"""
        constants = {
            'SM2': [
                self.SM2_CURVE_P, self.SM2_CURVE_A, self.SM2_CURVE_B,
                self.SM2_CURVE_N, self.SM2_CURVE_GX, self.SM2_CURVE_GY
            ],
            'SM3': [
                hex(t)[2:].upper().zfill(8) for t in self.SM3_T_ARRAY
            ] + [
                hex(iv)[2:].upper().zfill(8) for iv in self.SM3_IV
            ],
            'SM4': [
                ''.join([hex(b)[2:].upper().zfill(2) for b in self.SM4_SBOX]),
                ''.join([hex(ck)[2:].upper().zfill(8) for ck in self.SM4_CK]),
                ''.join([hex(fk)[2:].upper().zfill(8) for fk in self.SM4_FK])
            ]
        }
        return constants


class APKAnalyzer:
    """APK文件分析器"""

    def __init__(self, apk_path: str):
        self.apk_path = apk_path
        self.constants = SMCryptoConstants()
        self.java_candidates = []
        self.native_candidates = []
        self.filtered_candidates = []

    def extract_apk(self) -> Tuple[List[str], List[str]]:
        """提取APK文件，分离Java代码和Native代码"""
        java_files = []
        native_files = []

        try:
            with zipfile.ZipFile(self.apk_path, 'r') as apk_zip:
                for file_info in apk_zip.filelist:
                    filename = file_info.filename

                    # 检查文件类型
                    if filename.endswith('.dex'):
                        java_files.append(filename)
                    elif filename.endswith('.so'):
                        native_files.append(filename)

            logger.info(f"发现 {len(java_files)} 个DEX文件和 {len(native_files)} 个SO文件")
            return java_files, native_files

        except Exception as e:
            logger.error(f"提取APK文件失败: {e}")
            return [], []

    def analyze_java_code(self, dex_files: List[str]) -> List[Dict]:
        """分析Java代码部分（DEX文件）"""
        candidates = []

        if not ANDROGUARD_AVAILABLE:
            logger.warning("Androguard不可用，跳过Java代码分析")
            return candidates

        try:
            if ANDROGUARD_VERSION == "new":
                # 使用新版本的Androguard API
                candidates = self._analyze_java_code_new_api()
            else:
                # 使用旧版本的Androguard API
                candidates = self._analyze_java_code_old_api()

        except Exception as e:
            logger.error(f"Java代码分析失败: {e}")

        logger.info(f"Java代码分析完成，发现 {len(candidates)} 个候选项")
        return candidates

    def _analyze_java_code_new_api(self) -> List[Dict]:
        """使用新版本Androguard API分析Java代码"""
        candidates = []

        try:
            # 使用新版本的APK类
            from androguard.core.apk import APK
            from androguard.core.dex import DEX
            from androguard.core.analysis.analysis import Analysis

            apk_obj = APK(self.apk_path)

            # 新版本API的正确使用方式
            # 尝试不同的方法获取DEX对象
            dex_objects = []

            # 方法1: 尝试get_all_dex() - 处理生成器对象
            try:
                dex_generator = apk_obj.get_all_dex()
                # 将生成器转换为列表
                dex_data_list = list(dex_generator)
                logger.debug(f"使用get_all_dex()获取到 {len(dex_data_list)} 个DEX数据")

                # 将字节数据转换为DEX对象
                for dex_data in dex_data_list:
                    if isinstance(dex_data, bytes):
                        try:
                            dex_obj = DEX(dex_data)
                            dex_objects.append(dex_obj)
                            logger.debug(f"成功创建DEX对象，大小: {len(dex_data)} 字节")
                        except Exception as e:
                            logger.debug(f"创建DEX对象失败: {e}")
                    else:
                        # 如果已经是DEX对象，直接使用
                        dex_objects.append(dex_data)

            except Exception as e:
                logger.debug(f"get_all_dex()失败: {e}")

                # 方法2: 尝试get_dex() - 处理生成器对象
                try:
                    dex_generator = apk_obj.get_dex()
                    # 将生成器转换为列表
                    dex_data_list = list(dex_generator)
                    logger.debug(f"使用get_dex()获取到 {len(dex_data_list)} 个DEX数据")

                    for dex_data in dex_data_list:
                        if isinstance(dex_data, bytes):
                            try:
                                dex_obj = DEX(dex_data)
                                dex_objects.append(dex_obj)
                                logger.debug(f"成功创建DEX对象，大小: {len(dex_data)} 字节")
                            except Exception as e:
                                logger.debug(f"创建DEX对象失败: {e}")
                        else:
                            # 如果已经是DEX对象，直接使用
                            dex_objects.append(dex_data)

                except Exception as e2:
                    logger.debug(f"get_dex()也失败: {e2}")

                    # 方法3: 尝试直接迭代生成器
                    try:
                        dex_generator = apk_obj.get_dex()
                        for dex_data in dex_generator:
                            if isinstance(dex_data, bytes):
                                try:
                                    dex_obj = DEX(dex_data)
                                    dex_objects.append(dex_obj)
                                    logger.debug(f"成功创建DEX对象，大小: {len(dex_data)} 字节")
                                except Exception as e:
                                    logger.debug(f"创建DEX对象失败: {e}")
                            else:
                                dex_objects.append(dex_data)
                        logger.debug(f"使用迭代方式获取到 {len(dex_objects)} 个DEX对象")
                    except Exception as e3:
                        logger.debug(f"迭代方式也失败: {e3}")

            # 分析DEX对象
            for i, dex_obj in enumerate(dex_objects):
                try:
                    logger.debug(f"分析第 {i+1} 个DEX对象")

                    # 检查DEX对象是否有效并尝试不同的方法获取类
                    classes = []

                    # 方法1: 尝试get_classes()
                    if hasattr(dex_obj, 'get_classes'):
                        try:
                            classes = dex_obj.get_classes()
                            logger.debug(f"DEX对象 {i+1} 使用get_classes()获取到 {len(classes)} 个类")
                        except Exception as e:
                            logger.debug(f"DEX对象 {i+1} get_classes()失败: {e}")

                    # 方法2: 尝试get_classes_def()
                    elif hasattr(dex_obj, 'get_classes_def'):
                        try:
                            classes = dex_obj.get_classes_def()
                            logger.debug(f"DEX对象 {i+1} 使用get_classes_def()获取到 {len(classes)} 个类")
                        except Exception as e:
                            logger.debug(f"DEX对象 {i+1} get_classes_def()失败: {e}")

                    # 方法3: 检查对象类型和属性
                    else:
                        logger.debug(f"DEX对象 {i+1} 类型: {type(dex_obj)}")
                        logger.debug(f"DEX对象 {i+1} 属性: {[attr for attr in dir(dex_obj) if not attr.startswith('_')]}")

                        # 尝试其他可能的方法
                        for method_name in ['get_class_defs', 'classes', 'class_defs']:
                            if hasattr(dex_obj, method_name):
                                try:
                                    method = getattr(dex_obj, method_name)
                                    if callable(method):
                                        classes = method()
                                    else:
                                        classes = method
                                    logger.debug(f"DEX对象 {i+1} 使用{method_name}获取到 {len(classes)} 个类")
                                    break
                                except Exception as e:
                                    logger.debug(f"DEX对象 {i+1} {method_name}失败: {e}")

                    if not classes:
                        logger.debug(f"DEX对象 {i+1} 无法获取类列表，跳过")
                        continue

                    for class_def in classes:
                        try:
                            class_name = class_def.get_name()

                            # 检查类名
                            if self._check_crypto_keywords(class_name):
                                candidates.append({
                                    'type': 'java_class',
                                    'name': class_name,
                                    'algorithm': self._identify_algorithm(class_name),
                                    'location': 'class_name'
                                })
                                logger.debug(f"发现国密算法相关类: {class_name}")

                            # 分析方法
                            try:
                                methods = class_def.get_methods()
                                for method in methods:
                                    try:
                                        method_name = method.get_name()

                                        # 检查方法名
                                        if self._check_crypto_keywords(method_name):
                                            candidates.append({
                                                'type': 'java_method',
                                                'class': class_name,
                                                'method': method_name,
                                                'algorithm': self._identify_algorithm(method_name),
                                                'location': 'method_name'
                                            })
                                            logger.debug(f"发现国密算法相关方法: {class_name}.{method_name}")
                                    except Exception as e:
                                        logger.debug(f"分析方法时出错: {e}")
                                        continue
                            except Exception as e:
                                logger.debug(f"获取类方法时出错: {e}")

                        except Exception as e:
                            logger.debug(f"分析类时出错: {e}")
                            continue

                except Exception as e:
                    logger.debug(f"分析DEX对象 {i+1} 时出错: {e}")
                    continue

        except Exception as e:
            logger.debug(f"新版本API分析出错: {e}")
            # 如果新版本API失败，尝试备用方法
            try:
                candidates = self._analyze_java_code_fallback()
            except Exception as e2:
                logger.debug(f"备用方法也失败: {e2}")

        return candidates

    def _extract_method_strings_new_api(self, method, dex_obj) -> List[str]:
        """使用新版本API从方法中提取字符串常量"""
        strings = []
        try:
            # 在新版本中，字符串提取可能需要不同的方法
            # 这里提供一个简化的实现
            code = method.get_code()
            if code:
                # 尝试获取字节码并分析字符串引用
                # 这是一个简化的实现，可能需要根据实际API调整
                pass
        except Exception as e:
            logger.debug(f"新版本API提取字符串时出错: {e}")
        return strings

    def _analyze_java_code_fallback(self) -> List[Dict]:
        """备用的Java代码分析方法"""
        candidates = []

        try:
            # 简单的字符串搜索方法
            with zipfile.ZipFile(self.apk_path, 'r') as apk_zip:
                # 分析AndroidManifest.xml
                try:
                    manifest_data = apk_zip.read('AndroidManifest.xml')
                    manifest_str = manifest_data.decode('utf-8', errors='ignore')

                    # 在manifest中查找类名
                    import re
                    class_pattern = r'android:name="([^"]*)"'
                    matches = re.findall(class_pattern, manifest_str)

                    for class_name in matches:
                        if self._check_crypto_keywords(class_name):
                            candidates.append({
                                'type': 'java_class',
                                'name': class_name,
                                'algorithm': self._identify_algorithm(class_name),
                                'location': 'manifest'
                            })
                except Exception as e:
                    logger.debug(f"分析AndroidManifest.xml时出错: {e}")

                # 分析DEX文件中的字符串
                for file_name in apk_zip.namelist():
                    if file_name.endswith('.dex'):
                        try:
                            dex_data = apk_zip.read(file_name)
                            dex_str = dex_data.decode('utf-8', errors='ignore')

                            # 简单的字符串搜索
                            for keyword in ['SM2', 'SM3', 'SM4', 'SMS4']:
                                if keyword in dex_str:
                                    # 尝试提取包含关键字的上下文
                                    start = 0
                                    while True:
                                        pos = dex_str.find(keyword, start)
                                        if pos == -1:
                                            break

                                        # 提取上下文
                                        context_start = max(0, pos - 30)
                                        context_end = min(len(dex_str), pos + 30)
                                        context = dex_str[context_start:context_end].strip()

                                        if context and len(context) > len(keyword):
                                            candidates.append({
                                                'type': 'java_string',
                                                'value': context,
                                                'algorithm': self._identify_algorithm(keyword),
                                                'location': f'dex_string_{file_name}'
                                            })

                                        start = pos + 1

                        except Exception as e:
                            logger.debug(f"分析DEX文件 {file_name} 时出错: {e}")

        except Exception as e:
            logger.debug(f"备用分析方法出错: {e}")

        return candidates

    def _analyze_java_code_old_api(self) -> List[Dict]:
        """使用旧版本Androguard API分析Java代码"""
        candidates = []

        try:
            # 使用旧版本的API
            apk_obj = apk.APK(self.apk_path)
            dex_list = apk_obj.get_dex()

            for dex_data in dex_list:
                dvm_obj = dvm.DalvikVMFormat(dex_data)
                analysis_obj = analysis.Analysis(dvm_obj)

                # 分析类和方法
                for class_obj in dvm_obj.get_classes():
                    class_name = class_obj.get_name()

                    # 检查类名是否包含国密算法关键字
                    if self._check_crypto_keywords(class_name):
                        candidates.append({
                            'type': 'java_class',
                            'name': class_name,
                            'algorithm': self._identify_algorithm(class_name),
                            'location': 'class_name'
                        })

                    # 分析方法
                    for method in class_obj.get_methods():
                        method_name = method.get_name()

                        # 检查方法名
                        if self._check_crypto_keywords(method_name):
                            candidates.append({
                                'type': 'java_method',
                                'class': class_name,
                                'method': method_name,
                                'algorithm': self._identify_algorithm(method_name),
                                'location': 'method_name'
                            })

                        # 分析方法中的字符串和常量
                        try:
                            method_analysis = analysis_obj.get_method(method)
                            if method_analysis:
                                # 获取方法中的字符串
                                strings = self._extract_method_strings(method, dvm_obj)
                                for string_val in strings:
                                    if self._check_crypto_keywords(string_val):
                                        candidates.append({
                                            'type': 'java_string',
                                            'class': class_name,
                                            'method': method_name,
                                            'value': string_val,
                                            'algorithm': self._identify_algorithm(string_val),
                                            'location': 'method_string'
                                        })

                                # 检查常量匹配
                                constants_found = self._check_constants_in_method(method, dvm_obj)
                                for const_info in constants_found:
                                    candidates.append({
                                        'type': 'java_constant',
                                        'class': class_name,
                                        'method': method_name,
                                        'constant': const_info['value'],
                                        'algorithm': const_info['algorithm'],
                                        'location': 'method_constant'
                                    })
                        except Exception as e:
                            logger.debug(f"分析方法 {method_name} 时出错: {e}")
                            continue

        except Exception as e:
            logger.debug(f"旧版本API分析出错: {e}")

        return candidates

    def analyze_native_code(self, so_files: List[str]) -> List[Dict]:
        """分析Native代码部分（SO文件）"""
        candidates = []

        if not PYELFTOOLS_AVAILABLE:
            logger.warning("pyelftools不可用，跳过Native代码分析")
            return candidates

        try:
            with zipfile.ZipFile(self.apk_path, 'r') as apk_zip:
                for so_file in so_files:
                    try:
                        # 提取SO文件
                        so_data = apk_zip.read(so_file)

                        # 分析ELF文件
                        from io import BytesIO
                        elf_file = ELFFile(BytesIO(so_data))

                        # 分析数据段
                        rodata_candidates = self._analyze_elf_section(elf_file, '.rodata', so_file)
                        data_candidates = self._analyze_elf_section(elf_file, '.data', so_file)
                        candidates.extend(rodata_candidates)
                        candidates.extend(data_candidates)

                        # 如果没有找到标准段，直接在整个文件中搜索
                        if not rodata_candidates and not data_candidates:
                            raw_candidates = self._analyze_raw_binary(so_data, so_file)
                            candidates.extend(raw_candidates)

                        # 分析导出函数名
                        function_candidates = self._analyze_elf_functions(elf_file, so_file)
                        candidates.extend(function_candidates)

                    except Exception as e:
                        logger.debug(f"分析SO文件 {so_file} 时出错: {e}")
                        continue

        except Exception as e:
            logger.error(f"Native代码分析失败: {e}")

        logger.info(f"Native代码分析完成，发现 {len(candidates)} 个候选项")
        return candidates

    def _analyze_raw_binary(self, binary_data: bytes, so_file: str) -> List[Dict]:
        """直接在原始二进制数据中搜索国密算法常量"""
        candidates = []
        constants = self.constants.get_all_constants()

        try:
            # 转换为十六进制字符串
            hex_data = binascii.hexlify(binary_data).decode('utf-8').upper()

            # 检查每个算法的常量
            for algorithm, const_list in constants.items():
                for const_val in const_list:
                    if const_val in hex_data:
                        candidates.append({
                            'type': 'native_constant',
                            'file': so_file,
                            'section': 'raw_binary',
                            'constant': const_val,
                            'algorithm': algorithm,
                            'location': 'raw_binary_search'
                        })
                        logger.debug(f"在 {so_file} 的原始数据中发现 {algorithm} 常量")
        except Exception as e:
            logger.debug(f"分析原始二进制数据时出错: {e}")

        return candidates

    def _check_crypto_keywords(self, text: str) -> bool:
        """检查文本是否包含国密算法关键字"""
        keywords = ['SM2', 'SM3', 'SM4', 'SMS4']
        text_upper = text.upper()
        return any(keyword in text_upper for keyword in keywords)

    def _identify_algorithm(self, text: str) -> str:
        """识别算法类型"""
        text_upper = text.upper()
        if 'SM2' in text_upper:
            return 'SM2'
        elif 'SM3' in text_upper:
            return 'SM3'
        elif 'SM4' in text_upper or 'SMS4' in text_upper:
            return 'SM4'
        return 'UNKNOWN'

    def _extract_method_strings(self, method, dvm_obj) -> List[str]:
        """从方法中提取字符串常量"""
        strings = []
        try:
            # 获取方法的字节码
            code = method.get_code()
            if code:
                # 遍历指令寻找字符串引用
                for instruction in code.get_bc().get_instructions():
                    if instruction.get_name() == 'const-string':
                        string_idx = instruction.get_ref_kind()
                        string_val = dvm_obj.get_string(string_idx)
                        if string_val:
                            strings.append(string_val)
        except Exception as e:
            logger.debug(f"提取方法字符串时出错: {e}")
        return strings

    def _check_constants_in_method(self, method, dvm_obj) -> List[Dict]:
        """检查方法中是否包含国密算法常量"""
        found_constants = []
        constants = self.constants.get_all_constants()

        try:
            code = method.get_code()
            if code:
                # 获取方法的原始字节码
                raw_code = code.get_raw()
                hex_code = binascii.hexlify(raw_code).decode('utf-8').upper()

                # 检查每个算法的常量
                for algorithm, const_list in constants.items():
                    for const_val in const_list:
                        if const_val in hex_code:
                            found_constants.append({
                                'algorithm': algorithm,
                                'value': const_val,
                                'type': 'constant_match'
                            })
        except Exception as e:
            logger.debug(f"检查方法常量时出错: {e}")

        return found_constants

    def _analyze_elf_section(self, elf_file, section_name: str, so_file: str) -> List[Dict]:
        """分析ELF文件的特定段"""
        candidates = []
        constants = self.constants.get_all_constants()

        try:
            section = elf_file.get_section_by_name(section_name)
            if section:
                section_data = section.data()
                hex_data = binascii.hexlify(section_data).decode('utf-8').upper()

                # 检查每个算法的常量
                for algorithm, const_list in constants.items():
                    for const_val in const_list:
                        if const_val in hex_data:
                            candidates.append({
                                'type': 'native_constant',
                                'file': so_file,
                                'section': section_name,
                                'constant': const_val,
                                'algorithm': algorithm,
                                'location': f'{section_name}_section'
                            })
        except Exception as e:
            logger.debug(f"分析ELF段 {section_name} 时出错: {e}")

        return candidates

    def _analyze_elf_functions(self, elf_file, so_file: str) -> List[Dict]:
        """分析ELF文件的导出函数"""
        candidates = []

        try:
            # 获取符号表
            symbol_tables = [s for s in elf_file.iter_sections()
                           if s.name in ['.symtab', '.dynsym']]

            for symbol_table in symbol_tables:
                for symbol in symbol_table.iter_symbols():
                    symbol_name = symbol.name
                    if symbol_name and self._check_crypto_keywords(symbol_name):
                        candidates.append({
                            'type': 'native_function',
                            'file': so_file,
                            'function': symbol_name,
                            'algorithm': self._identify_algorithm(symbol_name),
                            'location': 'function_name'
                        })
        except Exception as e:
            logger.debug(f"分析ELF函数时出错: {e}")

        return candidates

    def filter_interference(self, candidates: List[Dict]) -> List[Dict]:
        """过滤干扰项"""
        filtered = []

        for candidate in candidates:
            if self._is_interference(candidate):
                logger.debug(f"过滤干扰项: {candidate}")
                continue
            filtered.append(candidate)

        logger.info(f"过滤前: {len(candidates)} 个候选项，过滤后: {len(filtered)} 个候选项")
        return filtered

    def _is_interference(self, candidate: Dict) -> bool:
        """判断是否为干扰项"""
        # 检查Base64编码
        if self._is_base64_interference(candidate):
            return True

        # 检查Lambda表达式
        if self._is_lambda_interference(candidate):
            return True

        # 检查其他同名不同义的字符串
        if self._is_homonym_interference(candidate):
            return True

        return False

    def _is_base64_interference(self, candidate: Dict) -> bool:
        """检查是否为Base64编码干扰项"""
        if 'value' in candidate:
            value = candidate['value']
            # 检查是否为长段Base64编码（通常出现在证书中）
            if len(value) > 100 and re.match(r'^[A-Za-z0-9+/=]+$', value):
                # 尝试Base64解码
                try:
                    import base64
                    decoded = base64.b64decode(value).decode('utf-8', errors='ignore')
                    # 如果解码后不包含国密算法关键字，则为干扰项
                    if not self._check_crypto_keywords(decoded):
                        return True
                except:
                    pass
        return False

    def _is_lambda_interference(self, candidate: Dict) -> bool:
        """检查是否为Lambda表达式干扰项"""
        if 'class' in candidate or 'method' in candidate:
            class_name = candidate.get('class', '')
            method_name = candidate.get('method', '')
            # 检查是否为Lambda表达式生成的类或方法
            if 'lambda$' in class_name.lower() or 'lambda$' in method_name.lower():
                return True
        return False

    def _is_homonym_interference(self, candidate: Dict) -> bool:
        """检查是否为同名不同义的干扰项"""
        interference_patterns = [
            r'.*[iI]s[mM]3[uU]8.*',  # isM3U8 (HLS协议)
            r'.*[gG][sS][mM]2.*',    # GSM2 (通信协议)
            r'.*[sS][mM][sS].*[sS]ervice.*',  # SMS服务相关
        ]

        text_to_check = ''
        if 'value' in candidate:
            text_to_check = candidate['value']
        elif 'name' in candidate:
            text_to_check = candidate['name']
        elif 'function' in candidate:
            text_to_check = candidate['function']

        for pattern in interference_patterns:
            if re.match(pattern, text_to_check):
                return True

        return False

    def verify_algorithms(self, candidates: List[Dict]) -> List[Dict]:
        """验证国密算法实现"""
        verified = []

        for candidate in candidates:
            verification_result = self._verify_single_candidate(candidate)
            if verification_result:
                candidate['verification'] = verification_result
                verified.append(candidate)

        logger.info(f"验证完成，确认 {len(verified)} 个有效的国密算法实现")
        return verified

    def _verify_single_candidate(self, candidate: Dict) -> Optional[Dict]:
        """验证单个候选项"""
        try:
            if candidate['type'].startswith('java_'):
                return self._verify_java_candidate(candidate)
            elif candidate['type'].startswith('native_'):
                return self._verify_native_candidate(candidate)
        except Exception as e:
            logger.debug(f"验证候选项时出错: {e}")
        return None

    def _verify_java_candidate(self, candidate: Dict) -> Optional[Dict]:
        """验证Java候选项"""
        # 这里可以实现具体的Java方法调用验证
        # 由于需要动态调试环境，这里提供基本的静态验证
        verification = {
            'method': 'static_analysis',
            'confidence': 'medium',
            'details': f"发现{candidate['algorithm']}算法相关的{candidate['type']}"
        }

        # 如果是常量匹配，置信度更高
        if candidate['type'] == 'java_constant':
            verification['confidence'] = 'high'
            verification['details'] += f"，匹配到算法常量: {candidate['constant'][:20]}..."

        # 检查是否为知名密码学库（提高置信度）
        class_name = candidate.get('name', candidate.get('class', ''))
        if class_name:
            # BouncyCastle密码学库
            if 'bouncycastle' in class_name.lower():
                verification['confidence'] = 'high'
                verification['details'] += "，来自BouncyCastle密码学库"
                verification['crypto_library'] = 'BouncyCastle'

            # 其他知名密码学库
            elif any(lib in class_name.lower() for lib in ['crypto', 'cipher', 'digest', 'signature']):
                if candidate['type'] == 'java_class':
                    verification['confidence'] = 'high'
                    verification['details'] += "，来自密码学相关类库"

            # 检查是否为核心算法实现类
            core_patterns = [
                'Engine', 'Digest', 'Cipher', 'Signer', 'KeyExchange',
                'ParameterSpec', 'KeyGen', 'AlgParams'
            ]
            if any(pattern in class_name for pattern in core_patterns):
                if verification['confidence'] == 'medium':
                    verification['confidence'] = 'high'
                verification['details'] += "，核心算法实现类"

        return verification

    def _verify_native_candidate(self, candidate: Dict) -> Optional[Dict]:
        """验证Native候选项"""
        verification = {
            'method': 'static_analysis',
            'confidence': 'medium',
            'details': f"发现{candidate['algorithm']}算法相关的{candidate['type']}"
        }

        # 如果是常量匹配，置信度更高
        if candidate['type'] == 'native_constant':
            verification['confidence'] = 'high'
            verification['details'] += f"，在{candidate['section']}段匹配到算法常量"

        # 检查是否为OpenSSL变体
        if 'openssl' in candidate.get('file', '').lower():
            verification['details'] += "，可能为OpenSSL库变体"
            verification['openssl_variant'] = True

        return verification

    def analyze(self) -> Dict:
        """执行完整的APK分析流程"""
        logger.info(f"开始分析APK文件: {self.apk_path}")

        # 步骤1: 提取APK文件
        java_files, native_files = self.extract_apk()
        if not java_files and not native_files:
            return {'error': 'APK文件提取失败或未找到相关文件'}

        # 步骤2: 分析Java代码
        java_candidates = self.analyze_java_code(java_files)
        self.java_candidates = java_candidates

        # 步骤3: 分析Native代码
        native_candidates = self.analyze_native_code(native_files)
        self.native_candidates = native_candidates

        # 步骤4: 合并候选项
        all_candidates = java_candidates + native_candidates

        # 步骤5: 过滤干扰项
        filtered_candidates = self.filter_interference(all_candidates)
        self.filtered_candidates = filtered_candidates

        # 步骤6: 验证算法
        verified_candidates = self.verify_algorithms(filtered_candidates)

        # 生成分析报告
        report = self._generate_report(verified_candidates)

        logger.info("APK分析完成")
        return report

    def _generate_report(self, verified_candidates: List[Dict]) -> Dict:
        """生成分析报告"""
        report = {
            'apk_file': self.apk_path,
            'analysis_summary': {
                'total_candidates': len(self.java_candidates) + len(self.native_candidates),
                'filtered_candidates': len(self.filtered_candidates),
                'verified_algorithms': len(verified_candidates)
            },
            'algorithms_found': {},
            'detailed_results': verified_candidates
        }

        # 统计发现的算法
        for candidate in verified_candidates:
            algorithm = candidate['algorithm']
            if algorithm not in report['algorithms_found']:
                report['algorithms_found'][algorithm] = {
                    'count': 0,
                    'types': set(),
                    'confidence_levels': []
                }

            report['algorithms_found'][algorithm]['count'] += 1
            report['algorithms_found'][algorithm]['types'].add(candidate['type'])
            if 'verification' in candidate:
                report['algorithms_found'][algorithm]['confidence_levels'].append(
                    candidate['verification']['confidence']
                )

        # 转换set为list以便JSON序列化
        for algorithm in report['algorithms_found']:
            report['algorithms_found'][algorithm]['types'] = list(
                report['algorithms_found'][algorithm]['types']
            )

        return report


def print_report(report: Dict):
    """打印分析报告"""
    print("\n" + "="*80)
    print("Android APK 国密算法检测报告")
    print("="*80)

    print(f"\nAPK文件: {report['apk_file']}")

    summary = report['analysis_summary']
    print(f"\n分析摘要:")
    print(f"  总候选项数: {summary['total_candidates']}")
    print(f"  过滤后候选项数: {summary['filtered_candidates']}")
    print(f"  验证的算法实现数: {summary['verified_algorithms']}")

    algorithms = report['algorithms_found']
    if algorithms:
        print(f"\n发现的国密算法:")
        for algorithm, info in algorithms.items():
            print(f"  {algorithm}:")
            print(f"    实现数量: {info['count']}")
            print(f"    发现类型: {', '.join(info['types'])}")
            if info['confidence_levels']:
                high_conf = info['confidence_levels'].count('high')
                medium_conf = info['confidence_levels'].count('medium')
                print(f"    置信度: 高({high_conf}) 中({medium_conf})")
    else:
        print("\n未发现国密算法实现")

    # 详细结果
    if report['detailed_results']:
        print(f"\n详细检测结果:")
        for i, result in enumerate(report['detailed_results'], 1):
            print(f"\n  [{i}] {result['algorithm']} - {result['type']}")
            if 'class' in result:
                print(f"      类: {result['class']}")
            if 'method' in result:
                print(f"      方法: {result['method']}")
            if 'file' in result:
                print(f"      文件: {result['file']}")
            if 'function' in result:
                print(f"      函数: {result['function']}")
            if 'verification' in result:
                verification = result['verification']
                print(f"      验证: {verification['details']}")
                print(f"      置信度: {verification['confidence']}")


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='Android APK 国密算法检测工具')
    parser.add_argument('apk_file', help='要分析的APK文件路径')
    parser.add_argument('-o', '--output', help='输出报告文件路径（JSON格式）')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    parser.add_argument('--no-java', action='store_true', help='跳过Java代码分析')
    parser.add_argument('--no-native', action='store_true', help='跳过Native代码分析')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 检查APK文件是否存在
    if not os.path.exists(args.apk_file):
        print(f"错误: APK文件不存在: {args.apk_file}")
        sys.exit(1)

    # 创建分析器并执行分析
    analyzer = APKAnalyzer(args.apk_file)
    report = analyzer.analyze()

    if 'error' in report:
        print(f"分析失败: {report['error']}")
        sys.exit(1)

    # 打印报告
    print_report(report)

    # 保存报告到文件
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"\n报告已保存到: {args.output}")
        except Exception as e:
            print(f"保存报告失败: {e}")


if __name__ == "__main__":
    main()